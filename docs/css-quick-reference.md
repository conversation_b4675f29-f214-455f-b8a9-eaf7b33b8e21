# CSS Quick Reference - Togeda.ai

## Component CSS Variables

### Tabs
```css
--tab-background          /* Inactive tab background */
--tab-foreground          /* Inactive tab text */
--tab-active-background   /* Active tab background */
--tab-active-foreground   /* Active tab text */
--tab-active-border       /* Active tab border */
```

### Toast/Notifications
```css
--toast-background        /* Toast background */
--toast-foreground        /* Toast text */
--toast-border           /* Toast border */
```

### Sidebar/Navigation
```css
--sidebar-background      /* Sidebar background */
--sidebar-foreground      /* Sidebar text */
--sidebar-primary         /* Active navigation items */
--sidebar-primary-foreground /* Active nav text */
--sidebar-accent          /* Hover states */
--sidebar-accent-foreground   /* Hover text */
--sidebar-border          /* Sidebar borders */
```

### Switch/Toggle
```css
--switch-background       /* Switch track */
--switch-thumb           /* Switch thumb */
--switch-thumb-checked   /* Checked thumb */
```

### Focus System
```css
--ring                   /* Focus ring color */
--ring-offset           /* Focus ring offset color */
```

## Common Patterns

### Focus Ring Implementation
```tsx
className={cn(
  "focus-visible:outline-none",
  "focus-visible:ring-2 focus-visible:ring-[hsl(var(--ring))]",
  "focus-visible:ring-offset-2 focus-visible:ring-offset-[hsl(var(--ring-offset))]"
)}
```

### Component Background
```tsx
className="bg-[hsl(var(--component-background))] text-[hsl(var(--component-foreground))]"
```

### Interactive States
```tsx
className={cn(
  "hover:bg-[hsl(var(--component-hover))]",
  "active:bg-[hsl(var(--component-active))]",
  "disabled:opacity-50 disabled:cursor-not-allowed"
)}
```

## Do's and Don'ts

### ✅ Do
- Use CSS variables for all colors
- Include proper focus indicators
- Test in both light and dark themes
- Follow accessibility guidelines
- Use semantic HTML elements

### ❌ Don't
- Use `!important` declarations
- Hardcode color values
- Disable focus indicators
- Use generic global selectors
- Override component internals

## Accessibility Checklist

- [ ] Focus rings visible and high contrast
- [ ] Keyboard navigation works
- [ ] Screen reader compatible
- [ ] Color contrast ≥ 4.5:1 for text
- [ ] Color contrast ≥ 3:1 for UI elements
- [ ] No color-only information conveyance

## Theme Colors

### Light Mode
- Background: `#F5F5F5` (Warm White)
- Primary: `#00796B` (Deep Teal)
- Text: `#212121` (Rich Black)

### Dark Mode
- Background: `hsl(240 10% 3.9%)`
- Primary: `#00796B` (Deep Teal)
- Text: `hsl(0 0% 98%)`

## Quick Commands

### Add New CSS Variable
1. Add to `app/globals.css` in both light and dark sections
2. Add to `tailwind.config.ts` if needed as utility class
3. Use in components with `hsl(var(--variable-name))`

### Test Accessibility
```bash
# Run accessibility test page
open test-accessibility.html

# Test keyboard navigation
# Use Tab, Shift+Tab, Enter, Space, Arrow keys
```

### Build and Test
```bash
npm run build    # Check for build errors
npm run dev      # Test in development
```
