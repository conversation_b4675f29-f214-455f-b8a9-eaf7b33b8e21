"use client"

import React from "react"
import { Badge } from "@/components/ui/badge"
import { Lightbulb } from "lucide-react"
import { cn } from "@/lib/utils"

interface UserSuggestedBadgeProps {
  className?: string
  variant?: "default" | "secondary" | "outline"
}

export function UserSuggestedBadge({ className, variant = "secondary" }: UserSuggestedBadgeProps) {
  return (
    <Badge
      variant={variant}
      className={cn(
        "flex items-center gap-1 text-xs font-medium",
        "bg-gradient-to-r from-[#00796B]/10 to-[#FFD54F]/10",
        "border border-[#00796B]/20 text-[#00796B]",
        className
      )}
    >
      <Lightbulb className="h-3 w-3" />
      User-Suggested
    </Badge>
  )
}
