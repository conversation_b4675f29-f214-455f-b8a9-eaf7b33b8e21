import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  runTransaction,
  serverTimestamp,
  Timestamp,
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscriptionEntry,
  UserSubscriptionEntryCreateData,
  UserSubscriptionEntryUpdateData,
  SubscriptionSource,
  SubscriptionEntryStatus,
  StripeSubscriptionData,
  PerkSubscriptionData,
  GiveawaySubscriptionData,
  FreeSubscriptionData,
  createStripeSubscriptionEntry,
  createPerkSubscriptionEntry,
  createGiveawaySubscriptionEntry,
  createFreeSubscriptionEntry,
  validateSubscriptionEntry,
  SUBSCRIPTION_PRECEDENCE,
} from "./user-subscription.types"
import { convertCurrentPeriodEnd, convertEndDate, isSubscriptionPeriodValid } from "./date-utils"

/**
 * Flat subscription service for userSubscriptions/{subscriptionId} collection
 */
export class FlatSubscriptionService {
  private static readonly COLLECTION = "userSubscriptions"

  /**
   * Get current active subscription for a user
   * IMPROVED: Proper precedence-based subscription resolution
   */
  static async getCurrentSubscription(userId: string): Promise<UserSubscriptionEntry | null> {
    try {
      // Step 1: Check for any applied subscription (highest precedence first)
      const appliedQuery = query(
        collection(db, this.COLLECTION),
        where("userId", "==", userId),
        where("status", "==", "applied"),
        orderBy("precedence", "asc"), // Order by precedence to get highest priority first
        limit(1)
      )

      const appliedSnapshot = await getDocs(appliedQuery)

      // If user has an applied subscription, return it
      if (!appliedSnapshot.empty) {
        const doc = appliedSnapshot.docs[0]
        return { id: doc.id, ...doc.data() } as UserSubscriptionEntry
      }

      // Step 2: No applied subscription found - check for premium subscriptions (precedence < 999)
      const premiumQuery = query(
        collection(db, this.COLLECTION),
        where("userId", "==", userId),
        where("precedence", "<", 999), // All non-free subscriptions
        where("status", "!=", "expired"), // Exclude already applied subscriptions
        orderBy("precedence", "asc"),
        limit(1)
      )

      const premiumSnapshot = await getDocs(premiumQuery)

      if (!premiumSnapshot.empty) {
        // Found a premium subscription that's not applied - activate it
        const doc = premiumSnapshot.docs[0]
        const subscription = { id: doc.id, ...doc.data() } as UserSubscriptionEntry

        console.log(
          `Activating premium subscription ${subscription.id} (${subscription.source}) for user ${userId}`
        )

        // Update to applied status
        await this.updateSubscriptionEntry(subscription.id, {
          status: "applied",
          updatedAt: new Date() as any,
        })

        return {
          ...subscription,
          status: "applied",
          updatedAt: new Date() as any,
        }
      }

      // Step 3: No premium subscriptions - check for free subscription
      const freeQuery = query(
        collection(db, this.COLLECTION),
        where("userId", "==", userId),
        where("source", "==", "free"),
        limit(1)
      )

      const freeSnapshot = await getDocs(freeQuery)

      if (!freeSnapshot.empty) {
        // Found free subscription - ensure it's applied
        const doc = freeSnapshot.docs[0]
        const freeSubscription = { id: doc.id, ...doc.data() } as UserSubscriptionEntry

        if (freeSubscription.status !== "applied") {
          console.log(`Activating free subscription ${freeSubscription.id} for user ${userId}`)

          // Update to applied status
          await this.updateSubscriptionEntry(freeSubscription.id, {
            status: "applied",
            updatedAt: new Date() as any,
          })

          return {
            ...freeSubscription,
            status: "applied",
            updatedAt: new Date() as any,
          }
        }

        return freeSubscription
      }

      // Step 4: No subscriptions found - create free subscription
      console.log(`No subscriptions found for user ${userId}, creating free subscription`)

      const freeSubResult = await this.addFreeSubscription(userId)
      if (freeSubResult.success && freeSubResult.data) {
        return freeSubResult.data
      } else {
        console.error(`Failed to create free subscription for user ${userId}:`, freeSubResult.error)
      }

      return null
    } catch (error) {
      console.error("Error getting current subscription:", error)
      return null
    }
  }

  /**
   * Get all subscriptions for a user
   */
  static async getUserSubscriptions(userId: string): Promise<UserSubscriptionEntry[]> {
    try {
      // Now that indexes are deployed, we can use orderBy for better performance
      const q = query(
        collection(db, this.COLLECTION),
        where("userId", "==", userId),
        orderBy("precedence", "asc")
      )

      const snapshot = await getDocs(q)
      return snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserSubscriptionEntry[]
    } catch (error) {
      console.error("Error getting user subscriptions:", error)
      return []
    }
  }

  /**
   * Get subscriptions for multiple users (for squad member badges)
   */
  static async getMultiUserSubscriptions(userIds: string[]): Promise<UserSubscriptionEntry[]> {
    try {
      if (userIds.length === 0) return []

      // Firestore 'in' queries are limited to 10 items
      const chunks = []
      for (let i = 0; i < userIds.length; i += 10) {
        chunks.push(userIds.slice(i, i + 10))
      }

      const allSubscriptions: UserSubscriptionEntry[] = []

      for (const chunk of chunks) {
        const q = query(
          collection(db, this.COLLECTION),
          where("userId", "in", chunk),
          where("status", "==", "applied")
        )

        const snapshot = await getDocs(q)
        const subscriptions = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as UserSubscriptionEntry[]

        allSubscriptions.push(...subscriptions)
      }

      return allSubscriptions
    } catch (error) {
      console.error("Error getting multi-user subscriptions:", error)
      return []
    }
  }

  /**
   * Create a subscription entry
   */
  static async createSubscriptionEntry(
    entryData: UserSubscriptionEntryCreateData
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    try {
      // Validate entry data
      const validationErrors = validateSubscriptionEntry(entryData)
      if (validationErrors.length > 0) {
        return {
          success: false,
          error: new Error(`Validation failed: ${validationErrors.join(", ")}`),
        }
      }

      return await runTransaction(db, async (transaction) => {
        // If this is not a free subscription, ensure user doesn't already have an applied subscription
        if (entryData.source !== "free") {
          const existingApplied = await this.getCurrentSubscription(entryData.userId)
          if (existingApplied) {
            // Pause the existing subscription if new one has higher precedence
            if (entryData.precedence < existingApplied.precedence) {
              const pauseData: UserSubscriptionEntryUpdateData = {
                status: "paused",
                pausedAt: new Date() as any,
                activeDays: this.calculateActiveDays(existingApplied),
              }

              const existingRef = doc(db, this.COLLECTION, existingApplied.id)
              transaction.update(existingRef, {
                ...pauseData,
                updatedAt: serverTimestamp(),
              })
            } else {
              // New subscription has lower precedence, set it as pending
              entryData.status = "pending"
            }
          }
        }

        // Create new subscription entry
        const entryRef = doc(collection(db, this.COLLECTION))

        transaction.set(entryRef, {
          ...entryData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        const createdEntry: UserSubscriptionEntry = {
          id: entryRef.id,
          ...entryData,
          createdAt: null, // Will be set by server
          updatedAt: null,
        }

        return { success: true, id: entryRef.id, data: createdEntry }
      })
    } catch (error) {
      console.error("Error creating subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Update a subscription entry
   */
  static async updateSubscriptionEntry(
    subscriptionId: string,
    updateData: UserSubscriptionEntryUpdateData
  ): Promise<ServiceResponse> {
    try {
      const entryRef = doc(db, this.COLLECTION, subscriptionId)

      await updateDoc(entryRef, {
        ...updateData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete a subscription entry
   */
  static async deleteSubscriptionEntry(subscriptionId: string): Promise<ServiceResponse> {
    try {
      const entryRef = doc(db, this.COLLECTION, subscriptionId)
      await deleteDoc(entryRef)

      return { success: true }
    } catch (error) {
      console.error("Error deleting subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Add Stripe subscription
   */
  static async addStripeSubscription(
    userId: string,
    stripeData: StripeSubscriptionData
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    const entryData = createStripeSubscriptionEntry(userId, stripeData)
    return await this.createSubscriptionEntry(entryData)
  }

  /**
   * Add perk subscription
   */
  static async addPerkSubscription(
    userId: string,
    perkData: PerkSubscriptionData
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    const entryData = createPerkSubscriptionEntry(userId, perkData)
    return await this.createSubscriptionEntry(entryData)
  }

  /**
   * Add giveaway subscription
   */
  static async addGiveawaySubscription(
    userId: string,
    giveawayData: GiveawaySubscriptionData
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    const entryData = createGiveawaySubscriptionEntry(userId, giveawayData)
    return await this.createSubscriptionEntry(entryData)
  }

  /**
   * Add free subscription (for new users) - ATOMIC VERSION
   */
  static async addFreeSubscription(
    userId: string
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    try {
      // Use transaction to prevent race conditions
      return await runTransaction(db, async (transaction) => {
        // Check if user already has a free subscription within the transaction
        const existingQuery = query(
          collection(db, this.COLLECTION),
          where("userId", "==", userId),
          where("source", "==", "free"),
          limit(1)
        )

        const existingSnapshot = await getDocs(existingQuery)

        if (!existingSnapshot.empty) {
          // Free subscription already exists, return it
          const doc = existingSnapshot.docs[0]
          return {
            success: true,
            data: { id: doc.id, ...doc.data() } as UserSubscriptionEntry,
          }
        }

        // Create new free subscription entry
        const entryData = createFreeSubscriptionEntry(userId)
        const docRef = doc(collection(db, this.COLLECTION))

        transaction.set(docRef, {
          ...entryData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        return {
          success: true,
          data: { id: docRef.id, ...entryData } as UserSubscriptionEntry,
        }
      })
    } catch (error) {
      console.error("Error adding free subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Ensure user has a free subscription (create if doesn't exist) - ATOMIC VERSION
   */
  static async ensureFreeSubscription(userId: string): Promise<ServiceResponse> {
    try {
      const result = await this.addFreeSubscription(userId)
      return { success: result.success, error: result.error }
    } catch (error) {
      console.error("Error ensuring free subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Process expired subscriptions for a user
   */
  static async processExpiredSubscriptions(userId: string): Promise<
    ServiceResponse<{
      expiredCount: number
      reactivatedSubscription: UserSubscriptionEntry | null
    }>
  > {
    try {
      return await runTransaction(db, async (transaction) => {
        const userSubscriptions = await this.getUserSubscriptions(userId)
        const now = new Date()
        let expiredCount = 0
        let reactivatedSubscription: UserSubscriptionEntry | null = null

        // Mark expired subscriptions
        for (const subscription of userSubscriptions) {
          if (subscription.endDate && subscription.status !== "expired") {
            const endDate = convertEndDate(subscription.endDate)

            if (!endDate) {
              console.warn(
                `Unable to parse endDate for subscription ${subscription.id}:`,
                subscription.endDate
              )
              continue // Skip this subscription if we can't parse the date
            }

            if (endDate <= now) {
              const subscriptionRef = doc(db, this.COLLECTION, subscription.id)
              transaction.update(subscriptionRef, {
                status: "expired",
                updatedAt: serverTimestamp(),
              })
              expiredCount++

              // Log specific information for perk subscriptions
              if (subscription.source === "perk") {
                const perkData = subscription.subscriptionData as PerkSubscriptionData
                // console.log(
                //   `Expired perk subscription ${subscription.id} for user ${userId}, perk: ${perkData.perkId}`
                // )
              }
            }
          }
        }

        // If current applied subscription expired, find next highest precedence
        const currentSubscription = userSubscriptions.find((sub) => sub.status === "applied")
        if (currentSubscription && currentSubscription.endDate) {
          const endDate =
            currentSubscription.endDate instanceof Date
              ? currentSubscription.endDate
              : currentSubscription.endDate.toDate()

          if (endDate <= now) {
            // Find next highest precedence subscription to activate
            const pendingSubscriptions = userSubscriptions
              .filter((sub) => sub.status === "paused" || sub.status === "pending")
              .sort((a, b) => a.precedence - b.precedence)

            if (pendingSubscriptions.length > 0) {
              const nextSubscription = pendingSubscriptions[0]
              const subscriptionRef = doc(db, this.COLLECTION, nextSubscription.id)

              // Calculate new end date if it was paused
              let updateData: any = {
                status: "applied",
                updatedAt: serverTimestamp(),
              }

              if (nextSubscription.activeDays && nextSubscription.pausedAt) {
                const remainingDays = this.calculateRemainingDays(nextSubscription)
                if (remainingDays > 0) {
                  const newEndDate = new Date(now.getTime() + remainingDays * 24 * 60 * 60 * 1000)
                  updateData.endDate = newEndDate
                }
              }

              transaction.update(subscriptionRef, updateData)
              reactivatedSubscription = { ...nextSubscription, ...updateData }
            }
          }
        }

        return {
          success: true,
          data: { expiredCount, reactivatedSubscription },
        }
      })
    } catch (error) {
      console.error("Error processing expired subscriptions:", error)
      return { success: false, error }
    }
  }

  /**
   * Calculate active days for a subscription
   */
  private static calculateActiveDays(subscription: UserSubscriptionEntry): number {
    if (!subscription.startDate) return 0

    const startDate =
      subscription.startDate instanceof Date
        ? subscription.startDate
        : subscription.startDate.toDate()

    const now = new Date()
    const diffTime = Math.abs(now.getTime() - startDate.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * Calculate remaining days for a paused subscription
   */
  private static calculateRemainingDays(subscription: UserSubscriptionEntry): number {
    if (!subscription.endDate || !subscription.activeDays) return 0

    const originalEndDate =
      subscription.endDate instanceof Date ? subscription.endDate : subscription.endDate.toDate()

    const startDate =
      subscription.startDate instanceof Date
        ? subscription.startDate
        : subscription.startDate.toDate()

    const totalDays = Math.ceil(
      (originalEndDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    )
    return Math.max(0, totalDays - subscription.activeDays)
  }

  /**
   * Get subscription summary for a user
   */
  static async getSubscriptionSummary(userId: string): Promise<{
    isSubscribed: boolean
    currentPlan: string | null
    currentSource: string | null
    totalSubscriptions: number
    activeSubscriptions: number
    pausedSubscriptions: number
    expiredSubscriptions: number
  }> {
    try {
      const subscriptions = await this.getUserSubscriptions(userId)
      const currentSubscription = await this.getCurrentSubscription(userId)

      return {
        isSubscribed: currentSubscription?.source !== "free",
        currentPlan:
          currentSubscription?.source === "stripe"
            ? (currentSubscription.subscriptionData as StripeSubscriptionData).subscriptionPlan
            : null,
        currentSource: currentSubscription?.source || null,
        totalSubscriptions: subscriptions.length,
        activeSubscriptions: subscriptions.filter((s) => s.status === "applied").length,
        pausedSubscriptions: subscriptions.filter((s) => s.status === "paused").length,
        expiredSubscriptions: subscriptions.filter((s) => s.status === "expired").length,
      }
    } catch (error) {
      console.error("Error getting subscription summary:", error)
      return {
        isSubscribed: false,
        currentPlan: null,
        currentSource: null,
        totalSubscriptions: 0,
        activeSubscriptions: 0,
        pausedSubscriptions: 0,
        expiredSubscriptions: 0,
      }
    }
  }

  /**
   * Check if user has access to a specific feature
   */
  static async hasFeatureAccess(userId: string, feature: string): Promise<boolean> {
    try {
      const currentSubscription = await this.getCurrentSubscription(userId)
      if (!currentSubscription) return false

      switch (feature) {
        case "trip_chat":
          return currentSubscription.source !== "free"
        case "unlimited_ai":
          return currentSubscription.source !== "free"
        case "multiple_squads":
          return currentSubscription.source !== "free"
        default:
          return false
      }
    } catch (error) {
      console.error("Error checking feature access:", error)
      return false
    }
  }

  /**
   * Get subscription limits for a user with enhanced validation
   * FIXED: Added validation to ensure subscription status is correctly determined
   */
  static async getSubscriptionLimits(userId: string): Promise<{
    maxSquads: number
    maxTripsPerSquad: number
    maxDailyAIRequests: number
    maxWeeklyAIRequests: number
    hasTripChat: boolean
  }> {
    try {
      const currentSubscription = await this.getCurrentSubscription(userId)

      // CRITICAL FIX: Enhanced validation for subscription status
      const isSubscribed = await this.validateSubscriptionStatus(userId, currentSubscription)

      // Import limits from types (assuming they exist)
      const SUBSCRIPTION_LIMITS = {
        FREE: {
          MAX_SQUADS: 1,
          MAX_TRIPS_PER_SQUAD: 2,
          MAX_DAILY_AI_REQUESTS: 10,
          MAX_WEEKLY_AI_REQUESTS: 50,
          HAS_TRIP_CHAT: false,
        },
        PRO: {
          MAX_SQUADS: 5,
          MAX_TRIPS_PER_SQUAD: 10,
          MAX_DAILY_AI_REQUESTS: 100,
          MAX_WEEKLY_AI_REQUESTS: 500,
          HAS_TRIP_CHAT: true,
        },
      }

      const limits = isSubscribed ? SUBSCRIPTION_LIMITS.PRO : SUBSCRIPTION_LIMITS.FREE

      // console.log(
      //   `User ${userId} subscription status: ${isSubscribed ? "PRO" : "FREE"} (source: ${currentSubscription?.source || "none"})`
      // )

      return {
        maxSquads: limits.MAX_SQUADS,
        maxTripsPerSquad: limits.MAX_TRIPS_PER_SQUAD,
        maxDailyAIRequests: limits.MAX_DAILY_AI_REQUESTS,
        maxWeeklyAIRequests: limits.MAX_WEEKLY_AI_REQUESTS,
        hasTripChat: limits.HAS_TRIP_CHAT,
      }
    } catch (error) {
      console.error("Error getting subscription limits:", error)
      return {
        maxSquads: 1,
        maxTripsPerSquad: 2,
        maxDailyAIRequests: 10,
        maxWeeklyAIRequests: 50,
        hasTripChat: false,
      }
    }
  }

  /**
   * Validate subscription status to prevent unauthorized upgrades
   * ADDED: New validation method to ensure subscription status is correctly determined
   */
  static async validateSubscriptionStatus(
    userId: string,
    currentSubscription: UserSubscriptionEntry | null
  ): Promise<boolean> {
    try {
      // If no subscription, user is definitely free
      if (!currentSubscription) {
        // console.log(`User ${userId} has no subscription - FREE status`)
        return false
      }

      // If subscription source is free, user is free
      if (currentSubscription.source === "free") {
        // console.log(`User ${userId} has free subscription - FREE status`)
        return false
      }

      // For Stripe subscriptions, validate the subscription is actually active
      if (currentSubscription.source === "stripe") {
        const stripeData = currentSubscription.subscriptionData as StripeSubscriptionData
        const isActive = stripeData.subscriptionStatus === "active"

        // Check if subscription period is still valid
        const isPeriodValid = isSubscriptionPeriodValid(stripeData.currentPeriodEnd)

        const isValidStripe = isActive && isPeriodValid
        // console.log(
        //   `User ${userId} Stripe subscription validation: active=${isActive}, periodValid=${isPeriodValid}, result=${isValidStripe}`
        // )
        return isValidStripe
      }

      // For perk subscriptions, validate the perk is still valid and not expired
      if (currentSubscription.source === "perk") {
        const perkData = currentSubscription.subscriptionData as PerkSubscriptionData

        // Check if perk subscription has expired
        if (currentSubscription.endDate) {
          const endDate =
            currentSubscription.endDate instanceof Date
              ? currentSubscription.endDate
              : currentSubscription.endDate.toDate()

          const isNotExpired = endDate > new Date()
          console.log(
            `User ${userId} perk subscription validation: expires=${endDate.toISOString()}, notExpired=${isNotExpired}`
          )
          return isNotExpired
        }

        // If no end date, check if it's a permanent perk (shouldn't happen for subscription perks)
        // console.log(`User ${userId} perk subscription has no end date - treating as valid`)
        return true
      }

      // For giveaway subscriptions, similar validation to perks
      if (currentSubscription.source === "giveaway") {
        if (currentSubscription.endDate) {
          const endDate =
            currentSubscription.endDate instanceof Date
              ? currentSubscription.endDate
              : currentSubscription.endDate.toDate()

          const isNotExpired = endDate > new Date()
          // console.log(
          //   `User ${userId} giveaway subscription validation: expires=${endDate.toISOString()}, notExpired=${isNotExpired}`
          // )
          return isNotExpired
        }

        // console.log(`User ${userId} giveaway subscription has no end date - treating as valid`)
        return true
      }

      // Unknown subscription source - default to free for security
      console.warn(
        `User ${userId} has unknown subscription source: ${currentSubscription.source} - defaulting to FREE`
      )
      return false
    } catch (error) {
      console.error(`Error validating subscription status for user ${userId}:`, error)
      // Default to free on error for security
      return false
    }
  }
}
